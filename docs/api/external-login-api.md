# 第三方應用登錄接口 API 文檔

## 接口概述

**接口路徑：** `/api/external/v1/login`  
**請求方法：** `POST`  
**接口描述：** 第三方應用登錄認證接口，用於外部系統獲取訪問令牌  
**認證方式：** RSA加密的AppID和AppKey

---

## 請求參數

### 請求頭 (Headers)

| 參數名 | 類型 | 必填 | 描述 |
|--------|------|------|------|
| `External-App-Id` | String | ✅ | 經過RSA加密的應用ID |
| `External-App-Key` | String | ✅ | 經過RSA加密的應用密鑰 |
| `Content-Type` | String | ✅ | 固定值：`application/json` |

### 請求體 (Body)

此接口無需請求體參數。

---

## 響應格式

### 成功響應

**HTTP狀態碼：** `200`

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "token": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
    "displayName": "用戶顯示名稱",
    "organizationList": [
      {
        "id": "org001",
        "name": "組織名稱",
        "type": "company"
      }
    ]
  }
}
```

**響應字段說明：**

| 字段名 | 類型 | 描述 |
|--------|------|------|
| `code` | Integer | 響應狀態碼，0表示成功，-1表示失敗 |
| `msg` | String | 響應消息描述 |
| `data.token` | String | 生成的訪問令牌，用於後續API調用 |
| `data.displayName` | String | 用戶顯示名稱 |
| `data.organizationList` | Array | 用戶所屬組織列表 |

### 錯誤響應

**HTTP狀態碼：** `200`（業務錯誤）

```json
{
  "code": -1,
  "msg": "錯誤描述信息",
  "data": null
}
```

---

## 錯誤碼說明

| 錯誤信息 | 原因 | 解決方案 |
|----------|------|----------|
| `請求需要輸入 AppID 及 AppKey` | 請求頭缺少必要的認證參數 | 確保請求頭包含 `External-App-Id` 和 `External-App-Key` |
| `沒有訪問權限` | 提供的AppID和AppKey無效或未激活 | 檢查AppID和AppKey是否正確，並確保已在系統中激活 |
| `帳號未啟用` | 關聯的用戶賬號未啟用 | 聯繫管理員啟用對應的用戶賬號 |
| `未找到对应的记录` | AppID和AppKey組合不存在 | 確認AppID和AppKey的正確性 |

---

## 調用示例

### cURL

```bash
curl -X POST "https://your-domain.com/api/external/v1/login" \
  -H "Content-Type: application/json" \
  -H "External-App-Id: [RSA加密後的AppID]" \
  -H "External-App-Key: [RSA加密後的AppKey]"
```

### JavaScript (Fetch API)

```javascript
const response = await fetch('/api/external/v1/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'External-App-Id': '[RSA加密後的AppID]',
    'External-App-Key': '[RSA加密後的AppKey]'
  }
});

const result = await response.json();

if (result.code === 0) {
  console.log('登錄成功');
  console.log('Token:', result.data.token);
  console.log('用戶名:', result.data.displayName);
  console.log('組織列表:', result.data.organizationList);
} else {
  console.error('登錄失敗:', result.msg);
}
```

### Java (Spring RestTemplate)

```java
// 設置請求頭
HttpHeaders headers = new HttpHeaders();
headers.setContentType(MediaType.APPLICATION_JSON);
headers.set("External-App-Id", "[RSA加密後的AppID]");
headers.set("External-App-Key", "[RSA加密後的AppKey]");

// 創建請求實體
HttpEntity<String> entity = new HttpEntity<>(headers);

// 發送請求
RestTemplate restTemplate = new RestTemplate();
ResponseEntity<ResultBean> response = restTemplate.exchange(
    "/api/external/v1/login",
    HttpMethod.POST,
    entity,
    ResultBean.class
);

// 處理響應
ResultBean result = response.getBody();
if (result.getCode() == 0) {
    Map<String, Object> data = (Map<String, Object>) result.getData();
    String token = (String) data.get("token");
    String displayName = (String) data.get("displayName");
    // 使用token進行後續操作
}
```

### Python (requests)

```python
import requests

url = "https://your-domain.com/api/external/v1/login"
headers = {
    "Content-Type": "application/json",
    "External-App-Id": "[RSA加密後的AppID]",
    "External-App-Key": "[RSA加密後的AppKey]"
}

response = requests.post(url, headers=headers)
result = response.json()

if result["code"] == 0:
    token = result["data"]["token"]
    display_name = result["data"]["displayName"]
    print(f"登錄成功，Token: {token}")
else:
    print(f"登錄失敗: {result['msg']}")
```

---

## 重要說明

### 🔐 安全要求

1. **RSA加密：** `External-App-Id` 和 `External-App-Key` 必須使用系統提供的RSA公鑰進行加密
2. **密鑰保護：** 請妥善保管原始的AppID和AppKey，避免洩露
3. **HTTPS：** 生產環境必須使用HTTPS協議

### 🎯 Token使用

1. **後續認證：** 成功獲取的 `token` 用於後續API調用的身份認證
2. **請求頭設置：** 在其他API請求中使用 `x-auth-token` 頭部傳遞token
3. **有效期管理：** Token具有一定的有效期，過期後需要重新登錄

### 📝 使用流程

1. 獲取AppID和AppKey（聯繫系統管理員）
2. 使用RSA公鑰加密AppID和AppKey
3. 調用登錄接口獲取token
4. 使用token調用其他業務接口

---

## 後續API調用示例

獲取token後，在其他需要認證的API請求中使用：

```bash
curl -X GET "https://your-domain.com/api/some-endpoint" \
  -H "Content-Type: application/json" \
  -H "x-auth-token: [從登錄接口獲取的token]"
```

---

## 聯繫支持

如有問題，請聯繫技術支持團隊獲取幫助。

**文檔版本：** v1.0  
**最後更新：** 2025-07-07
