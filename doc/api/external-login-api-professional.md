# ESG系統第三方應用登錄接口技術規範

**文檔版本：** v2.0  
**發布日期：** 2025-07-07  
**適用環境：** 生產環境  
**維護團隊：** ESG系統技術團隊

---

## 1. 概述

### 1.1 接口描述

本接口為ESG系統提供的第三方應用登錄認證服務，採用RSA非對稱加密技術確保通信安全。第三方系統通過此接口可獲取訪問令牌，用於後續業務API的身份認證。

### 1.2 技術特性

- **安全性**：採用RSA 1024位加密算法
- **認證方式**：基於AppID/AppKey的雙重認證
- **會話管理**：支持單用戶單會話模式
- **令牌有效期**：7200分鐘（5天）
- **協議支持**：HTTPS/TLS 1.2+

### 1.3 適用場景

- 第三方業務系統集成
- 外部數據同步服務
- 合作夥伴系統對接
- 移動應用後端服務

---

## 2. 接口規範

### 2.1 基本信息

| 項目 | 內容 |
|------|------|
| **接口路徑** | `/api/external/v1/login` |
| **請求方法** | `POST` |
| **內容類型** | `application/json` |
| **字符編碼** | `UTF-8` |
| **協議要求** | `HTTPS` |

### 2.2 請求規範

#### 2.2.1 請求頭部

| 參數名稱 | 數據類型 | 必填 | 長度限制 | 描述 |
|----------|----------|------|----------|------|
| `Content-Type` | String | ✓ | - | 固定值：`application/json` |
| `External-App-Id` | String | ✓ | ≤ 512 | RSA加密後的應用標識符 |
| `External-App-Key` | String | ✓ | ≤ 512 | RSA加密後的應用密鑰 |

#### 2.2.2 請求體

本接口無需請求體參數。

### 2.3 響應規範

#### 2.3.1 響應頭部

| 參數名稱 | 數據類型 | 描述 |
|----------|----------|------|
| `Content-Type` | String | `application/json; charset=utf-8` |
| `Cache-Control` | String | `no-cache, no-store, must-revalidate` |

#### 2.3.2 響應體結構

```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "token": "A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6",
    "displayName": "系統用戶",
    "organizationList": [
      {
        "id": "uuid-string",
        "name": "組織名稱",
        "no": "組織編號",
        "companyName": "所屬公司",
        "unitCode": "單位代碼",
        "nameWithCompanyName": "完整組織名稱"
      }
    ]
  }
}
```

#### 2.3.3 響應字段定義

**根級字段：**

| 字段名 | 數據類型 | 必填 | 描述 |
|--------|----------|------|------|
| `code` | Integer | ✓ | 業務狀態碼，0=成功，-1=失敗 |
| `msg` | String | ✓ | 響應消息描述 |
| `data` | Object | ✓ | 業務數據對象 |

**data對象字段：**

| 字段名 | 數據類型 | 必填 | 長度 | 描述 |
|--------|----------|------|------|------|
| `token` | String | ✓ | 32 | 訪問令牌，用於後續API認證 |
| `displayName` | String | ✓ | ≤ 100 | 用戶顯示名稱 |
| `organizationList` | Array | ✓ | - | 用戶關聯組織列表 |

**organizationList數組元素：**

| 字段名 | 數據類型 | 必填 | 長度 | 描述 |
|--------|----------|------|------|------|
| `id` | String | ✓ | 36 | 組織唯一標識符（UUID格式） |
| `name` | String | ✓ | ≤ 200 | 組織名稱 |
| `no` | String | ✓ | ≤ 50 | 組織編號，用於層級關係標識 |
| `companyName` | String | ○ | ≤ 200 | 上級公司名稱 |
| `unitCode` | String | ○ | ≤ 50 | 業務單位代碼 |
| `nameWithCompanyName` | String | ○ | ≤ 400 | 包含公司名的完整組織名稱 |

---

## 3. 安全規範

### 3.1 加密算法

#### 3.1.1 RSA加密參數

| 參數 | 值 |
|------|-----|
| **密鑰長度** | 1024位 |
| **加密模式** | RSA/ECB/PKCS1Padding |
| **編碼格式** | Base64 |
| **字符編碼** | UTF-8 |

#### 3.1.2 生產環境公鑰

```
-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCGXG423pFrAhgDGtS1u70ggF+7
XHIQc4Jfsv0OzrCob6Wyt8pmD0PJJipXw3NW82Q/kxnDxaE6fxdiQlLK0uLqDKEi
bxjuJ2tSmGCJmykD4gzM0GobY06ukH076dNhvap5d3oKfeG8XCpYH9lI2SvXcSWb
pGRT1reU940xsO5onwIDAQAB
-----END PUBLIC KEY-----
```

### 3.2 憑證管理

#### 3.2.1 申請流程

1. **提交申請**：向ESG系統管理員提交第三方對接申請
2. **審核評估**：技術團隊評估對接需求和安全風險
3. **創建賬戶**：在系統中創建專用的第三方對接賬戶
4. **分配憑證**：生成並分配AppID和AppKey
5. **測試驗證**：在測試環境進行集成測試
6. **生產部署**：通過測試後可部署到生產環境

#### 3.2.2 憑證格式

| 憑證類型 | 格式 | 長度 | 示例 |
|----------|------|------|------|
| **AppID** | 字母數字組合 | 8-32位 | `app_partner_001` |
| **AppKey** | UUID格式 | 36位 | `550e8400-e29b-41d4-a716-************` |

### 3.3 安全要求

- **傳輸安全**：必須使用HTTPS協議
- **憑證保護**：AppID和AppKey需妥善保管，禁止硬編碼
- **加密強度**：所有敏感參數必須使用RSA加密
- **會話安全**：令牌具有時效性，過期自動失效
- **訪問控制**：基於IP白名單的訪問限制（可選）

---

## 4. 錯誤處理

### 4.1 HTTP狀態碼

| 狀態碼 | 描述 | 處理建議 |
|--------|------|----------|
| `200` | 請求成功 | 檢查業務狀態碼 |
| `400` | 請求參數錯誤 | 檢查請求格式和參數 |
| `401` | 認證失敗 | 檢查AppID和AppKey |
| `403` | 訪問被拒絕 | 檢查IP白名單配置 |
| `429` | 請求頻率超限 | 實施退避重試策略 |
| `500` | 服務器內部錯誤 | 聯繫技術支持 |

### 4.2 業務錯誤碼

| 錯誤碼 | 錯誤信息 | 原因分析 | 解決方案 |
|--------|----------|----------|----------|
| `-1` | `請求需要輸入 AppID 及 AppKey` | 請求頭缺少必要參數 | 檢查請求頭設置 |
| `-1` | `沒有訪問權限` | 憑證無效或未激活 | 驗證AppID和AppKey有效性 |
| `-1` | `帳號未啟用` | 關聯用戶賬戶被禁用 | 聯繫管理員啟用賬戶 |
| `-1` | `未找到对应的记录` | AppID和AppKey組合不存在 | 確認憑證正確性 |

### 4.3 異常處理建議

#### 4.3.1 重試策略

```
初始延遲：1秒
最大重試次數：3次
退避算法：指數退避
最大延遲：30秒
```

#### 4.3.2 日誌記錄

建議記錄以下信息：
- 請求時間戳
- 請求唯一標識
- 響應狀態碼
- 錯誤信息（脫敏後）
- 重試次數

---

## 5. 實施指南

### 5.1 開發環境配置

#### 5.1.1 依賴庫

**Java環境：**
```xml
<dependency>
    <groupId>org.springframework</groupId>
    <artifactId>spring-web</artifactId>
    <version>5.3.21</version>
</dependency>
```

**Python環境：**
```bash
pip install requests cryptography
```

#### 5.1.2 環境變量

```bash
# 應用憑證
ESG_APP_ID=your_app_id
ESG_APP_KEY=your_app_key

# 服務端點
ESG_API_BASE_URL=https://your-domain.com
ESG_LOGIN_ENDPOINT=/api/external/v1/login

# RSA公鑰
ESG_RSA_PUBLIC_KEY=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQ...
```

### 5.2 最佳實踐

#### 5.2.1 連接池配置

```java
// 建議的HTTP連接池配置
RestTemplate restTemplate = new RestTemplate();
HttpComponentsClientHttpRequestFactory factory = 
    new HttpComponentsClientHttpRequestFactory();
factory.setConnectTimeout(5000);        // 連接超時：5秒
factory.setReadTimeout(30000);          // 讀取超時：30秒
factory.setConnectionRequestTimeout(3000); // 請求超時：3秒
restTemplate.setRequestFactory(factory);
```

#### 5.2.2 令牌管理

```java
public class TokenManager {
    private String token;
    private LocalDateTime expireTime;
    
    public boolean isTokenValid() {
        return token != null && 
               expireTime != null && 
               LocalDateTime.now().isBefore(expireTime);
    }
    
    public void refreshTokenIfNeeded() {
        if (!isTokenValid()) {
            // 重新登錄獲取新令牌
            login();
        }
    }
}
```

### 5.3 性能優化

#### 5.3.1 緩存策略

- **令牌緩存**：將有效令牌緩存在內存中
- **公鑰緩存**：RSA公鑰可長期緩存
- **連接復用**：使用HTTP連接池提高性能

#### 5.3.2 並發控制

- **線程安全**：確保令牌管理的線程安全性
- **請求限流**：避免頻繁的登錄請求
- **異步處理**：對於非關鍵路徑可採用異步調用

---

## 6. 測試指南

### 6.1 單元測試

```java
@Test
public void testRSAEncryption() {
    String plainText = "test_app_id";
    String encrypted = RSAUtil.encrypt(plainText, PUBLIC_KEY);
    assertNotNull(encrypted);
    assertTrue(encrypted.length() > 0);
}

@Test
public void testLoginSuccess() {
    LoginResponse response = externalLoginService.login();
    assertEquals(0, response.getCode());
    assertNotNull(response.getData().getToken());
}
```

### 6.2 集成測試

#### 6.2.1 測試用例

| 測試場景 | 預期結果 | 驗證點 |
|----------|----------|--------|
| 正常登錄 | 返回有效令牌 | token非空，organizationList有數據 |
| 無效憑證 | 返回認證失敗 | code=-1，錯誤信息正確 |
| 加密錯誤 | 返回解密失敗 | 服務端無法解密參數 |
| 網絡異常 | 觸發重試機制 | 重試次數符合預期 |

#### 6.2.2 性能測試

```bash
# 使用Apache Bench進行壓力測試
ab -n 1000 -c 10 -H "External-App-Id: encrypted_id" \
   -H "External-App-Key: encrypted_key" \
   https://your-domain.com/api/external/v1/login
```

---

## 7. 運維監控

### 7.1 監控指標

| 指標名稱 | 類型 | 閾值 | 描述 |
|----------|------|------|------|
| 響應時間 | 延遲 | < 2秒 | 接口平均響應時間 |
| 成功率 | 比率 | > 99.5% | 請求成功率 |
| QPS | 吞吐量 | < 100/秒 | 每秒查詢數 |
| 錯誤率 | 比率 | < 0.5% | 業務錯誤率 |

### 7.2 告警配置

#### 7.2.1 告警規則

```yaml
alerts:
  - name: "ESG登錄接口響應時間過長"
    condition: "avg_response_time > 5s"
    duration: "2m"
    severity: "warning"
    
  - name: "ESG登錄接口錯誤率過高"
    condition: "error_rate > 5%"
    duration: "1m"
    severity: "critical"
```

### 7.3 日誌規範

#### 7.3.1 日誌格式

```json
{
  "timestamp": "2025-07-07T10:30:00.000Z",
  "level": "INFO",
  "service": "esg-external-api",
  "endpoint": "/api/external/v1/login",
  "method": "POST",
  "app_id": "app_***_001",
  "response_time": 1250,
  "status_code": 200,
  "business_code": 0,
  "user_agent": "Java/1.8.0_301",
  "ip": "*************"
}
```

---

## 8. 代碼示例

### 8.1 Java實現

#### 8.1.1 RSA加密工具類

```java
package com.example.esg.util;

import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * RSA加密工具類
 * 用於ESG系統第三方對接的參數加密
 */
public class RSAEncryptionUtil {

    private static final String ALGORITHM = "RSA";
    private static final String TRANSFORMATION = "RSA/ECB/PKCS1Padding";
    private static final String CHARSET = "UTF-8";

    // 生產環境RSA公鑰
    private static final String PUBLIC_KEY =
        "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCGXG423pFrAhgDGtS1u70ggF+7" +
        "XHIQc4Jfsv0OzrCob6Wyt8pmD0PJJipXw3NW82Q/kxnDxaE6fxdiQlLK0uLqDKEi" +
        "bxjuJ2tSmGCJmykD4gzM0GobY06ukH076dNhvap5d3oKfeG8XCpYH9lI2SvXcSWb" +
        "pGRT1reU940xsO5onwIDAQAB";

    /**
     * RSA公鑰加密
     * @param plainText 明文
     * @return Base64編碼的密文
     * @throws Exception 加密異常
     */
    public static String encrypt(String plainText) throws Exception {
        return encrypt(plainText, PUBLIC_KEY);
    }

    /**
     * RSA公鑰加密
     * @param plainText 明文
     * @param publicKeyStr Base64編碼的公鑰
     * @return Base64編碼的密文
     * @throws Exception 加密異常
     */
    public static String encrypt(String plainText, String publicKeyStr) throws Exception {
        // 解碼公鑰
        byte[] keyBytes = Base64.getDecoder().decode(publicKeyStr);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
        PublicKey publicKey = keyFactory.generatePublic(keySpec);

        // 執行加密
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(CHARSET));

        // 返回Base64編碼結果
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }
}
```

#### 8.1.2 登錄服務類

```java
package com.example.esg.service;

import com.example.esg.util.RSAEncryptionUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * ESG外部登錄服務
 */
@Service
public class ESGExternalLoginService {

    @Value("${esg.api.base-url}")
    private String baseUrl;

    @Value("${esg.app.id}")
    private String appId;

    @Value("${esg.app.key}")
    private String appKey;

    private final RestTemplate restTemplate;

    public ESGExternalLoginService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    /**
     * 執行登錄操作
     * @return 登錄響應結果
     * @throws Exception 登錄異常
     */
    public LoginResponse login() throws Exception {
        // 加密憑證
        String encryptedAppId = RSAEncryptionUtil.encrypt(appId);
        String encryptedAppKey = RSAEncryptionUtil.encrypt(appKey);

        // 構建請求頭
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("External-App-Id", encryptedAppId);
        headers.set("External-App-Key", encryptedAppKey);

        // 發送請求
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);
        String url = baseUrl + "/api/external/v1/login";

        ResponseEntity<LoginResponse> response = restTemplate.exchange(
            url, HttpMethod.POST, requestEntity, LoginResponse.class);

        return response.getBody();
    }
}
```

#### 8.1.3 響應實體類

```java
package com.example.esg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * 登錄響應實體
 */
public class LoginResponse {
    private Integer code;
    private String msg;
    private LoginData data;

    // 標準getter/setter方法
    public Integer getCode() { return code; }
    public void setCode(Integer code) { this.code = code; }

    public String getMsg() { return msg; }
    public void setMsg(String msg) { this.msg = msg; }

    public LoginData getData() { return data; }
    public void setData(LoginData data) { this.data = data; }

    /**
     * 登錄數據實體
     */
    public static class LoginData {
        private String token;
        private String displayName;
        private List<Organization> organizationList;

        // getter/setter方法
        public String getToken() { return token; }
        public void setToken(String token) { this.token = token; }

        public String getDisplayName() { return displayName; }
        public void setDisplayName(String displayName) { this.displayName = displayName; }

        public List<Organization> getOrganizationList() { return organizationList; }
        public void setOrganizationList(List<Organization> organizationList) {
            this.organizationList = organizationList;
        }
    }

    /**
     * 組織實體
     */
    public static class Organization {
        private String id;
        private String name;
        private String no;
        private String companyName;
        private String unitCode;
        private String nameWithCompanyName;

        // getter/setter方法省略...
    }
}
```

### 8.2 Python實現

#### 8.2.1 加密工具模塊

```python
"""
ESG系統RSA加密工具模塊
"""
import base64
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.backends import default_backend

class RSAEncryptionUtil:
    """RSA加密工具類"""

    # 生產環境RSA公鑰
    PUBLIC_KEY_PEM = """-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCGXG423pFrAhgDGtS1u70ggF+7
XHIQc4Jfsv0OzrCob6Wyt8pmD0PJJipXw3NW82Q/kxnDxaE6fxdiQlLK0uLqDKEi
bxjuJ2tSmGCJmykD4gzM0GobY06ukH076dNhvap5d3oKfeG8XCpYH9lI2SvXcSWb
pGRT1reU940xsO5onwIDAQAB
-----END PUBLIC KEY-----"""

    @classmethod
    def encrypt(cls, plain_text: str, public_key_pem: str = None) -> str:
        """
        RSA公鑰加密

        Args:
            plain_text: 待加密的明文
            public_key_pem: PEM格式的公鑰，默認使用內置公鑰

        Returns:
            Base64編碼的密文

        Raises:
            Exception: 加密過程中的異常
        """
        if public_key_pem is None:
            public_key_pem = cls.PUBLIC_KEY_PEM

        try:
            # 載入公鑰
            public_key = serialization.load_pem_public_key(
                public_key_pem.encode('utf-8'),
                backend=default_backend()
            )

            # 執行加密
            encrypted = public_key.encrypt(
                plain_text.encode('utf-8'),
                padding.PKCS1v15()
            )

            # 返回Base64編碼結果
            return base64.b64encode(encrypted).decode('utf-8')

        except Exception as e:
            raise Exception(f"RSA加密失敗: {str(e)}")
```

#### 8.2.2 登錄服務模塊

```python
"""
ESG外部登錄服務模塊
"""
import requests
import logging
from typing import Dict, Any, Optional
from .encryption_util import RSAEncryptionUtil

logger = logging.getLogger(__name__)

class ESGExternalLoginService:
    """ESG外部登錄服務類"""

    def __init__(self, base_url: str, app_id: str, app_key: str, timeout: int = 30):
        """
        初始化登錄服務

        Args:
            base_url: ESG系統基礎URL
            app_id: 應用ID
            app_key: 應用密鑰
            timeout: 請求超時時間（秒）
        """
        self.base_url = base_url.rstrip('/')
        self.app_id = app_id
        self.app_key = app_key
        self.timeout = timeout
        self.session = requests.Session()

        # 配置請求會話
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'ESG-Python-Client/1.0'
        })

    def login(self) -> Dict[str, Any]:
        """
        執行登錄操作

        Returns:
            登錄響應結果字典

        Raises:
            Exception: 登錄過程中的異常
        """
        try:
            # 加密憑證
            encrypted_app_id = RSAEncryptionUtil.encrypt(self.app_id)
            encrypted_app_key = RSAEncryptionUtil.encrypt(self.app_key)

            # 構建請求頭
            headers = {
                'External-App-Id': encrypted_app_id,
                'External-App-Key': encrypted_app_key
            }

            # 發送請求
            url = f"{self.base_url}/api/external/v1/login"
            logger.info(f"發送登錄請求到: {url}")

            response = self.session.post(
                url,
                headers=headers,
                timeout=self.timeout
            )

            # 檢查HTTP狀態
            response.raise_for_status()

            # 解析響應
            result = response.json()

            # 檢查業務狀態
            if result.get('code') != 0:
                raise Exception(f"登錄失敗: {result.get('msg', '未知錯誤')}")

            logger.info("登錄成功")
            return result

        except requests.exceptions.RequestException as e:
            logger.error(f"網絡請求異常: {str(e)}")
            raise Exception(f"網絡請求失敗: {str(e)}")
        except Exception as e:
            logger.error(f"登錄異常: {str(e)}")
            raise

    def get_token(self) -> Optional[str]:
        """
        獲取訪問令牌

        Returns:
            訪問令牌字符串，失敗返回None
        """
        try:
            result = self.login()
            return result.get('data', {}).get('token')
        except Exception as e:
            logger.error(f"獲取令牌失敗: {str(e)}")
            return None
```

### 8.3 使用示例

#### 8.3.1 Java使用示例

```java
@Component
public class ESGIntegrationExample {

    @Autowired
    private ESGExternalLoginService loginService;

    public void demonstrateUsage() {
        try {
            // 執行登錄
            LoginResponse response = loginService.login();

            if (response.getCode() == 0) {
                String token = response.getData().getToken();
                String displayName = response.getData().getDisplayName();

                System.out.println("登錄成功！");
                System.out.println("用戶: " + displayName);
                System.out.println("令牌: " + token);

                // 使用令牌調用其他API
                callOtherAPIs(token);

            } else {
                System.err.println("登錄失敗: " + response.getMsg());
            }

        } catch (Exception e) {
            System.err.println("登錄異常: " + e.getMessage());
        }
    }

    private void callOtherAPIs(String token) {
        // 使用令牌調用其他業務API的示例
        HttpHeaders headers = new HttpHeaders();
        headers.set("x-auth-token", token);
        // ... 其他API調用邏輯
    }
}
```

#### 8.3.2 Python使用示例

```python
import os
from esg_client import ESGExternalLoginService

def main():
    """主函數示例"""
    # 從環境變量獲取配置
    base_url = os.getenv('ESG_API_BASE_URL', 'https://your-domain.com')
    app_id = os.getenv('ESG_APP_ID')
    app_key = os.getenv('ESG_APP_KEY')

    if not app_id or not app_key:
        print("錯誤: 請設置ESG_APP_ID和ESG_APP_KEY環境變量")
        return

    # 創建登錄服務實例
    login_service = ESGExternalLoginService(base_url, app_id, app_key)

    try:
        # 執行登錄
        result = login_service.login()

        # 提取登錄信息
        data = result['data']
        token = data['token']
        display_name = data['displayName']
        organizations = data['organizationList']

        print(f"登錄成功！")
        print(f"用戶: {display_name}")
        print(f"令牌: {token}")
        print(f"組織數量: {len(organizations)}")

        # 使用令牌調用其他API
        call_other_apis(token)

    except Exception as e:
        print(f"登錄失敗: {str(e)}")

def call_other_apis(token: str):
    """使用令牌調用其他API的示例"""
    headers = {
        'x-auth-token': token,
        'Content-Type': 'application/json'
    }
    # ... 其他API調用邏輯

if __name__ == '__main__':
    main()
```

---

## 9. 附錄

### 8.1 版本歷史

| 版本 | 日期 | 變更內容 | 作者 |
|------|------|----------|------|
| v2.0 | 2025-07-07 | 完整重構文檔結構，增加專業規範 | 技術團隊 |
| v1.0 | 2025-01-01 | 初始版本 | 技術團隊 |

### 8.2 相關文檔

- [ESG系統API總覽](./api-overview.md)
- [第三方對接安全規範](./security-guidelines.md)
- [錯誤碼參考手冊](./error-codes.md)
- [SDK開發指南](./sdk-development.md)

### 8.3 技術支持

**聯繫方式：**
- 技術支持郵箱：<EMAIL>
- 開發者社區：https://developer.esg-system.com
- 緊急聯繫電話：+852-1234-5678

**服務時間：**
- 工作日：09:00-18:00 (GMT+8)
- 緊急支持：7×24小時

---

*本文檔受版權保護，未經授權不得複製或分發。*  
*© 2025 ESG系統技術團隊 版權所有*
